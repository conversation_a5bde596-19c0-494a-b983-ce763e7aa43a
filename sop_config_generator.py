#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
SOP 配置產生器
用於輕鬆創建和修改 SOP 步驟配置
"""

import json
import os

class SOPConfigGenerator:
    def __init__(self):
        self.available_actions = {
            'reaching': '伸手動作',
            'grasping': '抓取動作',
            'screwing': '螺絲動作',
            'rotating': '旋轉動作',
            'lifting': '舉起動作',
            'pushing': '推動動作',
            'pulling': '拉動動作',
            'connecting': '連接動作'
        }
        
    def create_interactive_config(self):
        """互動式創建 SOP 配置"""
        print("=== SOP 配置產生器 ===")
        print("請按照提示輸入您的 SOP 步驟")
        print()
        
        steps = []
        step_id = 0
        
        while True:
            print(f"--- 步驟 {step_id + 1} ---")
            
            # 輸入步驟名稱
            name = input("步驟名稱: ").strip()
            if not name:
                break
                
            # 輸入步驟描述
            description = input("步驟描述: ").strip()
            
            # 選擇預期動作
            print("\n可用的動作類型:")
            for i, (action, desc) in enumerate(self.available_actions.items(), 1):
                print(f"  {i}. {action} - {desc}")
            
            print("\n請輸入預期動作的編號 (用逗號分隔，例如: 1,3):")
            action_input = input("預期動作: ").strip()
            
            expected_actions = []
            try:
                action_indices = [int(x.strip()) - 1 for x in action_input.split(',')]
                action_list = list(self.available_actions.keys())
                expected_actions = [action_list[i] for i in action_indices if 0 <= i < len(action_list)]
            except:
                print("輸入格式錯誤，使用預設動作")
                expected_actions = ['reaching']
            
            # 輸入預估時間
            try:
                duration = int(input("預估完成時間 (秒): ").strip() or "60")
            except:
                duration = 60
            
            # 創建步驟
            step = {
                "id": step_id,
                "name": name,
                "description": description,
                "expected_actions": expected_actions,
                "duration_estimate": duration
            }
            
            steps.append(step)
            step_id += 1
            
            # 詢問是否繼續
            continue_input = input("\n是否繼續添加步驟? (y/n): ").strip().lower()
            if continue_input != 'y':
                break
            print()
        
        return self.create_config(steps)
    
    def create_config(self, steps):
        """創建完整的配置"""
        config = {
            "sop_steps": steps,
            "action_types": {
                "reaching": {
                    "name": "伸手動作",
                    "threshold": 0.5
                },
                "grasping": {
                    "name": "抓取動作", 
                    "threshold": 0.8
                },
                "screwing": {
                    "name": "螺絲動作",
                    "threshold": 0.7
                },
                "rotating": {
                    "name": "旋轉動作",
                    "threshold": 0.6
                },
                "lifting": {
                    "name": "舉起動作",
                    "threshold": 0.6
                },
                "pushing": {
                    "name": "推動動作",
                    "threshold": 0.6
                },
                "pulling": {
                    "name": "拉動動作",
                    "threshold": 0.6
                },
                "connecting": {
                    "name": "連接動作",
                    "threshold": 0.7
                }
            }
        }
        return config
    
    def save_config(self, config, filename="sop_config.json"):
        """儲存配置到檔案"""
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(config, f, ensure_ascii=False, indent=2)
            print(f"\n✅ 配置已儲存至 {filename}")
            return True
        except Exception as e:
            print(f"❌ 儲存失敗: {e}")
            return False
    
    def load_and_display_config(self, filename="sop_config.json"):
        """載入並顯示現有配置"""
        try:
            with open(filename, 'r', encoding='utf-8') as f:
                config = json.load(f)
            
            print(f"\n=== 當前 SOP 配置 ({filename}) ===")
            for i, step in enumerate(config['sop_steps']):
                print(f"{i+1}. {step['name']}")
                print(f"   描述: {step['description']}")
                print(f"   預期動作: {', '.join(step['expected_actions'])}")
                print(f"   預估時間: {step['duration_estimate']} 秒")
                print()
            
            return config
        except FileNotFoundError:
            print(f"❌ 檔案 {filename} 不存在")
            return None
        except Exception as e:
            print(f"❌ 載入失敗: {e}")
            return None

def main():
    generator = SOPConfigGenerator()
    
    while True:
        print("\n=== SOP 配置管理 ===")
        print("1. 創建新的 SOP 配置")
        print("2. 查看現有配置")
        print("3. 退出")
        
        choice = input("\n請選擇操作 (1-3): ").strip()
        
        if choice == '1':
            config = generator.create_interactive_config()
            if config['sop_steps']:
                filename = input("\n配置檔案名稱 (預設: sop_config.json): ").strip()
                if not filename:
                    filename = "sop_config.json"
                generator.save_config(config, filename)
        
        elif choice == '2':
            filename = input("配置檔案名稱 (預設: sop_config.json): ").strip()
            if not filename:
                filename = "sop_config.json"
            generator.load_and_display_config(filename)
        
        elif choice == '3':
            print("再見！")
            break
        
        else:
            print("無效的選擇，請重新輸入")

if __name__ == "__main__":
    main()
